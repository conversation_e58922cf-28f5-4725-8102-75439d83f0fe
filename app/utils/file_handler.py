"""File handling utilities"""

import mimetypes
import uuid
from pathlib import Path
from typing import Optional, <PERSON><PERSON>

import aiofiles
from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.models.file_record import FileRecord, FileType
from app.utils.zip_handler import ZipHandler


class FileHandler:
    """Handles file upload, validation, and storage"""

    @staticmethod
    async def save_upload_file(
        upload_file: UploadFile,
        subdir: str = "",
        conversion_id: Optional[str] = None,
        user_session: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Tuple[Path, Optional[FileRecord]]:
        """
        Save uploaded file to storage directory and record in database

        Args:
            upload_file: FastAPI UploadFile object
            subdir: Subdirectory within upload directory
            conversion_id: Optional conversion task ID
            user_session: Optional user session ID
            db: Optional database session

        Returns:
            Tuple of (Path to saved file, FileRecord if database recording enabled)
        """
        # Validate file size
        if upload_file.size and upload_file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes",
            )

        # Generate unique filename
        file_extension = (
            Path(upload_file.filename).suffix if upload_file.filename else ""
        )
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # Create target directory
        target_dir = settings.upload_dir / subdir
        target_dir.mkdir(parents=True, exist_ok=True)

        # Save file
        file_path = target_dir / unique_filename

        async with aiofiles.open(file_path, "wb") as f:
            content = await upload_file.read()
            await f.write(content)

        # Record in database if db session provided
        file_record = None
        if db is not None:
            try:
                file_size = FileHandler.get_file_size(file_path)
                mime_type = FileHandler.get_mime_type(file_path)

                file_record = FileRecord.create_record(
                    filename=unique_filename,
                    original_filename=upload_file.filename,
                    file_path=str(file_path),
                    file_type=FileType.UPLOAD,
                    file_size=file_size,
                    mime_type=mime_type,
                    conversion_id=conversion_id,
                    user_session=user_session,
                )

                db.add(file_record)
                db.commit()
                db.refresh(file_record)
            except Exception as e:
                # Log error but don't fail the file save operation
                print(f"Warning: Failed to record file in database: {e}")

        return file_path, file_record

    @staticmethod
    def record_output_file(
        file_path: Path,
        original_filename: Optional[str] = None,
        conversion_id: Optional[str] = None,
        user_session: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Optional[FileRecord]:
        """
        Record an output file in the database

        Args:
            file_path: Path to the output file
            original_filename: Original filename if different from current
            conversion_id: Optional conversion task ID
            user_session: Optional user session ID
            db: Optional database session

        Returns:
            FileRecord if database recording enabled, None otherwise
        """
        if db is None:
            return None

        try:
            file_size = FileHandler.get_file_size(file_path)
            mime_type = FileHandler.get_mime_type(file_path)

            file_record = FileRecord.create_record(
                filename=file_path.name,
                original_filename=original_filename,
                file_path=str(file_path),
                file_type=FileType.OUTPUT,
                file_size=file_size,
                mime_type=mime_type,
                conversion_id=conversion_id,
                user_session=user_session,
            )

            db.add(file_record)
            db.commit()
            db.refresh(file_record)
            return file_record
        except Exception as e:
            # Log error but don't fail the operation
            print(f"Warning: Failed to record output file in database: {e}")
            return None

    @staticmethod
    async def save_template_file(
        upload_file: UploadFile, name: str, resource_type: str
    ) -> Path:
        """
        Save template file with specific naming

        Args:
            upload_file: FastAPI UploadFile object
            name: Template name (will be used as filename)
            resource_type: Type of resource (docx_template, csl_style, etc.)

        Returns:
            Path to saved file
        """
        # Validate file size
        if upload_file.size and upload_file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes",
            )

        # Determine file extension
        original_extension = (
            Path(upload_file.filename).suffix if upload_file.filename else ""
        )

        # Create filename
        filename = f"{name}{original_extension}"

        # Determine subdirectory based on resource type
        subdir_map = {
            "docx_template": "docx",
            "csl_style": "csl",
            "bib_file": "bib",
            "lua_filter": "filters",
        }
        subdir = subdir_map.get(resource_type, "other")

        # Create target directory
        target_dir = settings.template_dir / subdir
        target_dir.mkdir(parents=True, exist_ok=True)

        # Save file
        file_path = target_dir / filename

        async with aiofiles.open(file_path, "wb") as f:
            content = await upload_file.read()
            await f.write(content)

        return file_path

    @staticmethod
    def get_mime_type(file_path: Path) -> Optional[str]:
        """Get MIME type of file"""
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type

    @staticmethod
    def get_file_size(file_path: Path) -> int:
        """Get file size in bytes"""
        return file_path.stat().st_size if file_path.exists() else 0

    @staticmethod
    def delete_file(file_path: Path) -> bool:
        """Delete file safely"""
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return False
        except Exception:
            return False

    @staticmethod
    def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
        """Validate file extension"""
        if not filename:
            return False

        extension = Path(filename).suffix.lower()
        return extension in [ext.lower() for ext in allowed_extensions]

    @staticmethod
    async def save_and_process_upload_file(
        upload_file: UploadFile,
        subdir: str = "",
        conversion_id: Optional[str] = None,
        user_session: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Tuple[Path, Optional[Path], Optional[FileRecord]]:
        """
        Save uploaded file and process if it's a ZIP file

        Args:
            upload_file: FastAPI UploadFile object
            subdir: Subdirectory within upload directory
            conversion_id: Optional conversion task ID
            user_session: Optional user session ID
            db: Optional database session

        Returns:
            Tuple of (main_file_path, extraction_directory_path, file_record)
            - If regular file: (file_path, None, file_record)
            - If ZIP file: (main_document_path, extraction_directory_path, file_record)

        Raises:
            HTTPException: If file is too large or ZIP processing fails
        """
        # First save the uploaded file
        saved_file_path, file_record = await FileHandler.save_upload_file(
            upload_file, subdir, conversion_id, user_session, db
        )

        # Check if it's a ZIP file
        if ZipHandler.is_zip_file(saved_file_path):
            try:
                # Extract ZIP and find main document
                extract_dir, main_file = ZipHandler.extract_zip(saved_file_path)

                # Clean up the original ZIP file
                FileHandler.delete_file(saved_file_path)

                # Update file record if exists to point to the main file
                if file_record and db:
                    try:
                        file_record.file_path = str(main_file)
                        file_record.filename = main_file.name
                        file_record.file_size = FileHandler.get_file_size(main_file)
                        file_record.mime_type = FileHandler.get_mime_type(main_file)
                        db.commit()
                    except Exception as e:
                        print(f"Warning: Failed to update file record after ZIP extraction: {e}")

                return main_file, extract_dir, file_record

            except ValueError as e:
                # Clean up on failure
                FileHandler.delete_file(saved_file_path)
                if file_record and db:
                    try:
                        file_record.mark_deleted()
                        db.commit()
                    except Exception:
                        pass
                raise HTTPException(
                    status_code=422,
                    detail=f"ZIP file processing failed: {str(e)}"
                )
            except Exception as e:
                # Clean up on failure
                FileHandler.delete_file(saved_file_path)
                if file_record and db:
                    try:
                        file_record.mark_deleted()
                        db.commit()
                    except Exception:
                        pass
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to process ZIP file: {str(e)}"
                )
        else:
            # Regular file
            return saved_file_path, None, file_record

    @staticmethod
    def cleanup_extraction(extract_dir: Optional[Path]) -> bool:
        """
        Clean up extraction directory if it exists

        Args:
            extract_dir: Directory to clean up (can be None)

        Returns:
            True if cleanup was successful or not needed
        """
        if extract_dir is None:
            return True
        return ZipHandler.cleanup_extraction(extract_dir)

    @staticmethod
    def is_zip_file(filename: str) -> bool:
        """
        Check if filename indicates a ZIP file

        Args:
            filename: Name of the file

        Returns:
            True if filename has ZIP extension
        """
        if not filename:
            return False
        return Path(filename).suffix.lower() == '.zip'
